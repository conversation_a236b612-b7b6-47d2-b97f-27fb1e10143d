<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';
import Menu from './components/Menu.vue';
import { useSpacesStore } from './stores/spacesStore';
import { useKanbanStore } from './stores/kanbanStore';
import { useRoute, useRouter } from 'vue-router';

const loading = ref(false);
const error = ref<string | null>(null);
const route = useRoute();
const router = useRouter();

// Function to reload the page
const reloadPage = () => {
    window.location.reload();
};

onMounted(async () => {
	loading.value = true;
	try {
		// Initialize spaces store
		const spacesStore = useSpacesStore();
		await spacesStore.init();

		if (route.path !== '/') {
			if (!route.params.spaceId) {
				// Check if there's a last visited space in local storage
				const lastVisitedSpace = localStorage.getItem('lastSpaceId');
				if (lastVisitedSpace) {
					await spacesStore.setCurrentSpace(lastVisitedSpace);
					return;
				}
			}
			await spacesStore.setCurrentSpace(route.params.spaceId as string);
			await useKanbanStore().setCurrentProject(route.params.projectId as string);
			return;
		}

		// Navigate to home space if available
		if (spacesStore.spaces.hasOwnProperty("home")) {
			await spacesStore.setCurrentSpace("home");
			router.push("/home");
			return;
		}
		// Navigate to the first space if no home space is available
		const spacesKeys = Object.keys(spacesStore.spaces);
		if (spacesKeys.length > 0) {
			const spaceId = spacesKeys[0];
			await spacesStore.setCurrentSpace(spaceId);
			router.push(`/${spaceId}`);
		} else {
			throw new Error('No spaces found');
		}
	} catch (err: any) {
		console.error('App initialization error:', err);
		error.value = err.message;
		ElMessage.error(`Failed to initialize app: ${err.message}`);
	} finally {
		loading.value = false;
	}
});
</script>

<template>
	<div v-if="loading" class="loading-container">
		<el-card class="loading-card">
			<el-skeleton :rows="3" animated />
			<div class="loading-text">Connecting to server...</div>
		</el-card>
	</div>
	<div v-else-if="error" class="error-container">
		<el-card class="error-card">
			<el-alert :title="error" type="error" :closable="false" show-icon />
			<div class="error-actions">
				<el-button type="primary" @click="reloadPage">Retry</el-button>
			</div>
		</el-card>
	</div>
	<el-container v-else>
		<el-aside width="auto">
			<Menu />
		</el-aside>
		<router-view />
	</el-container>
</template>

<style scoped>
.el-container {
	height: 100%;
}

.loading-container,
.error-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	width: 100vw;
	background-color: #f5f7fa;
}

.loading-card,
.error-card {
	width: 400px;
	max-width: 90%;
	padding: 20px;
}

.loading-text {
	margin-top: 20px;
	text-align: center;
	font-size: 16px;
	color: #909399;
}

.error-actions {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}
</style>