import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from './pages/Dashboard.vue'
import Kanban from './pages/Kanban/Kanban.vue'
import Settings from './pages/Settings.vue'
import Space from './pages/Space.vue'
import Profile from './pages/Profile.vue'

const routes = [
	{
		path: '/:spaceId',
		component: Space,
		props: true,
		meta: { requiresAuth: true },
		children: [
			{
				path: '',
				name: 'Dashboard',
				component: Dashboard,
				props: true
			},
			{
				path: 'kanban/:projectId',
				name: 'Kanban',
				component: Kanban,
				props: true
			},
		],
	},
	{
		path: '/profile',
		name: 'Profile',
		component: Profile,
		meta: { requiresAuth: true },
	},
	{
		path: '/settings',
		name: 'Settings',
		component: Settings,
	}
];

const router = createRouter({
	history: createWebHistory("enki"),
	routes,
});

function init(user: any) {
	router.beforeEach((to, _from, next) => {
		if (
			!to.matched.some(record => record.meta.requiresAuth)
			|| user.value
		) return next();
	});
}

export { init, router };