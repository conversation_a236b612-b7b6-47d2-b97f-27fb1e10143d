import { defineStore } from 'pinia';
import type { Column, Task, TaskStatus, NewTask, UpdateTask, Project } from '@/types';
import * as kiApi from '@/services/kiApi';

interface KanbanState {
  columns: Column[];
  tasks: Task[];
  currentProjectId: string;
  projects: Project[];
  serverUrl: string;
  loading: boolean;
  error: string | null;
}

export const useKanbanStore = defineStore('kanban', {
  state: (): KanbanState => ({
    columns: [
      { id: "todo", name: 'To Do' },
      { id: "doing", name: 'Doing' },
      { id: "review", name: 'Review' },
      { id: "done", name: 'Done' },
    ],
    tasks: [],
    currentProjectId: '',
    projects: [],
    serverUrl: '',
    loading: false,
    error: null,
  }),

  getters: {
    getTasksByStatus: (state) => (status: TaskStatus) => {
      return state.tasks
        .filter(task => task.status === status && task.parent_task_id !== undefined)
        .sort((a, b) => a.position - b.position);
    },

    getProjects: (state) => {
      return state.tasks.filter(task => task.parent_task_id === undefined);
    },
  },

  actions: {
    // Server connection
    // setServerUrl(url: string) {
    //   this.serverUrl = url;
    // },

    // async connectToServer() {
    //   this.loading = true;
    //   this.error = null;

    //   try {
    //     await kiApi.connectToKiServer(this.serverUrl);
    //     await this.loadProjects();
    //   } catch (error: any) {
    //     this.error = error.message;
    //   } finally {
    //     this.loading = false;
    //   }
    // },

    // Project actions
    async loadProjects(url: string) {
      this.serverUrl = url;
      this.loading = true;
      this.error = null;

      try {
        const projects = await kiApi.getProjects(this.serverUrl);
        // Store projects as tasks with parent_task_id = undefined
        const projectTasks = projects.map(p => ({ ...p, parent_task_id: undefined }));

        // Update the tasks array to include projects
        this.tasks = this.tasks.filter(t => t.parent_task_id !== undefined).concat(projectTasks);
        this.projects = projects;

        if (projects.length > 0 && !this.currentProjectId) {
          this.currentProjectId = projects[0].id;
          await this.loadTasks();
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async setCurrentProject(projectId: string) {
      if (this.projects.some(p => p.id === projectId)) {
        this.currentProjectId = projectId;
        await this.loadTasks();
      } else {
        this.error = `Project with ID ${projectId} not found`;
      }
    },

    async createProject(projectName: string, description?: string) {
      this.loading = true;
      this.error = null;

      try {
        const project = await kiApi.createProject(this.serverUrl, projectName, description);
        this.projects.push(project);

        // Add project to tasks array as well (with parent_task_id = undefined)
        const projectTask = { ...project, parent_task_id: undefined };
        this.tasks.push(projectTask);

        this.currentProjectId = project.id;
        // Clear only the regular tasks, keep projects
        this.tasks = this.tasks.filter(t => t.parent_task_id === undefined);
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    // Task actions
    async loadTasks() {
      if (!this.currentProjectId) return;

      this.loading = true;
      this.error = null;

      try {
        const tasks = await kiApi.getTasks(this.serverUrl, this.currentProjectId);
        // Keep projects and replace only regular tasks
        const projects = this.tasks.filter(t => t.parent_task_id === undefined);
        this.tasks = projects.concat(tasks);
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async addTask(taskData: {
      content: string,
      description?: string,
      columnId: string, // This is TaskStatus
      position: number,
      tags?: string[],
      assignees?: string[],
      start_time?: string | null,
      end_time?: string | null,
    }) {
      if (!this.currentProjectId) return;

      this.loading = true;
      this.error = null;

      try {
        const newTask: NewTask = {
          name: taskData.content,
          description: taskData.description,
          status: taskData.columnId as TaskStatus,
          position: taskData.position,
          tags: taskData.tags || [],
          assignees: taskData.assignees || [],
          start_time: taskData.start_time,
          end_time: taskData.end_time,
        };

        const task = await kiApi.createTask(
          this.serverUrl,
          this.currentProjectId,
          newTask
        );

        this.tasks.push(task);
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async updateTask(taskData: {
      id: string,
      content: string,
      description?: string,
      columnId: string, // This is TaskStatus
      position: number,
      tags?: string[],
      assignees?: string[],
      start_time?: string | null,
      end_time?: string | null,
    }) {
      this.loading = true;
      this.error = null;

      try {
        const updateData: UpdateTask = {
          name: taskData.content,
          description: taskData.description,
          status: taskData.columnId as TaskStatus,
          position: taskData.position,
          tags: taskData.tags,
          assignees: taskData.assignees,
          start_time: taskData.start_time,
          end_time: taskData.end_time,
        };

        const updatedTask = await kiApi.updateTask(
          this.serverUrl,
          taskData.id,
          updateData
        );

        // Update the task in the local state
        const index = this.tasks.findIndex(t => t.id === taskData.id);
        if (index !== -1) {
          this.tasks[index] = updatedTask;
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async deleteTask(taskId: string) {
      this.loading = true;
      this.error = null;

      try {
        await kiApi.deleteTask(this.serverUrl, taskId);

        // Remove the task from the local state
        const index = this.tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
          this.tasks.splice(index, 1);
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async moveTask(taskId: string, newStatus: TaskStatus, newPosition: number) {
      this.loading = true;
      this.error = null;

      try {
        const updatedTask = await kiApi.moveTask(
          this.serverUrl,
          taskId,
          newStatus,
          newPosition
        );

        // Update the task in the local state
        const index = this.tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
          this.tasks[index] = updatedTask;
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  }
});
