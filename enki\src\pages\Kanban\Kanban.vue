<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useKanbanStore } from '@/stores/kanbanStore';
import { FolderAdd } from '@element-plus/icons-vue';
import KanbanColumn from './KanbanColumn.vue';
import type { Task, TaskStatus } from '@/types';

const props = defineProps<{
  projectId: string;
}>();

const kanbanStore = useKanbanStore();

// State
const loading = computed(() => kanbanStore.loading);
const error = computed(() => kanbanStore.error);
const columns = computed(() => kanbanStore.columns);
const projects = computed(() => kanbanStore.projects);
const currentProjectId = computed(() => kanbanStore.currentProjectId);

// Placeholder for assignees - this should ideally come from a store or API
// and the items should have `value` and `label` properties for el-select,
// or map them in the template. Here, we assume `id` as value and `name` as label.
const allUsers = ref([
    { id: 'user1', name: '<PERSON>' },
    { id: 'user2', name: '<PERSON>' },
    { id: 'user3', name: '<PERSON>.' },
]);
// Get tasks by column ID (status)
const getTasksByColumnId = (columnId: TaskStatus) => {
  return kanbanStore.getTasksByStatus(columnId);
};

// Project dialog
const showProjectDialog = ref(false);
const newProjectName = ref('');
const newProjectDescription = ref('');

// Project methods
async function createNewProject() {
  if (!newProjectName.value.trim()) return;

  await kanbanStore.createProject(newProjectName.value, newProjectDescription.value);
  showProjectDialog.value = false;
  newProjectName.value = '';
  newProjectDescription.value = '';
}

// Task dialog
const showTaskDialog = ref(false);
const editingTask = ref<Task | null>(null);
const taskForm = ref({
	content: '',
	description: '',
	columnId: '' as TaskStatus | '',
	position: 0,
	tags: [] as string[],
	assignees: [] as string[],
	start_time: null as Date | string | null,
	end_time: null as Date | string | null,
});

// Task methods
function addTask(columnId: TaskStatus) {
	editingTask.value = null;
	taskForm.value = {
		content: '',
		description: '',
		columnId: columnId,
		position: getTasksByColumnId(columnId).length,
		tags: [],
		assignees: [],
		start_time: null,
		end_time: null,
	};
	showTaskDialog.value = true;
}

function editTask(task: Task) {
	editingTask.value = task;
	taskForm.value = {
		content: task.name,
		description: task.description || '',
		columnId: task.status, // This is TaskStatus
		position: task.position,
		tags: task.tags ? [...task.tags] : [],
		assignees: task.assignees ? [...task.assignees] : [], // Assuming assignees are stored as an array of IDs or simple strings
		start_time: task.start_time ? new Date(task.start_time) : null,
		end_time: task.end_time ? new Date(task.end_time) : null,
	};
	showTaskDialog.value = true;
}

async function saveTask() {
	if (!taskForm.value.content.trim() || !taskForm.value.columnId) {
		return;
	}

	const taskDataPayload = {
		content: taskForm.value.content,
		description: taskForm.value.description,
		columnId: taskForm.value.columnId as TaskStatus, // columnId is TaskStatus here
		position: taskForm.value.position,
		tags: taskForm.value.tags,
		assignees: taskForm.value.assignees,
		start_time: taskForm.value.start_time ? new Date(taskForm.value.start_time).toISOString() : null,
		end_time: taskForm.value.end_time ? new Date(taskForm.value.end_time).toISOString() : null,
	};

	if (editingTask.value) {
		await kanbanStore.updateTask({ id: editingTask.value.id, ...taskDataPayload });
	} else {
		await kanbanStore.addTask(taskDataPayload);
	}

	showTaskDialog.value = false;
}

async function deleteTask(taskId: string) {
	await kanbanStore.deleteTask(taskId);
}

async function handleMoveTask(taskId: string, newColumnId: string, newPosition: number) {
	await kanbanStore.moveTask(taskId, newColumnId as TaskStatus, newPosition);
}

// Watch for route changes to update the current project
watch(() => props.projectId, async (newProjectId: string | undefined) => {
  if (newProjectId && newProjectId !== currentProjectId.value) {
    await kanbanStore.setCurrentProject(newProjectId);
  }
});
</script>

<template>
	<div class="kanban-container">
		<div class="kanban-header">
			<div class="project-selector">
				<el-select v-model="currentProjectId" @change="kanbanStore.setCurrentProject" placeholder="Select Project">
					<el-option v-for="project in projects" :key="project.id" :label="project.name" :value="project.id" />
				</el-select>
				<el-button type="success" @click="showProjectDialog = true">
					<el-icon>
						<FolderAdd />
					</el-icon>
					New Project
				</el-button>
			</div>
		</div>

		<div v-if="loading" class="loading-container">
			<el-skeleton :rows="3" animated />
		</div>
		<div v-else-if="error" class="error-container">
			<el-alert :title="error" type="error" :closable="false" show-icon />
		</div>
		<div v-else class="kanban-board">
			<div class="columns-container">
				<kanban-column v-for="column in columns" :key="column.id" :column="column"
					:tasks="getTasksByColumnId(column.id)"
					@add-task="addTask" @edit-task="editTask" @delete-task="deleteTask" @move-task="handleMoveTask" />
			</div>
		</div>

		<!-- New Project Dialog -->
		<el-dialog v-model="showProjectDialog" title="Create New Project" width="30%">
			<el-form label-width="120px">
				<el-form-item label="Project Name" required>
					<el-input v-model="newProjectName" placeholder="Enter project name" />
				</el-form-item>
				<el-form-item label="Description">
					<el-input v-model="newProjectDescription" type="textarea" :rows="3" placeholder="Enter project description" />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="showProjectDialog = false">Cancel</el-button>
					<el-button type="primary" @click="createNewProject">Create</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- Add/Edit Task Dialog -->
		<el-dialog v-model="showTaskDialog" :title="editingTask ? 'Edit Task' : 'Add New Task'" width="30%">
			<el-form :model="taskForm" label-width="120px">
				<el-form-item label="Task Name" required>
					<el-input v-model="taskForm.content" type="textarea" :rows="3" placeholder="Enter task name" />
				</el-form-item>
				<el-form-item label="Description">
					<el-input v-model="taskForm.description" type="textarea" :rows="3"
						placeholder="Enter task description" />
				</el-form-item>
				<el-form-item v-if="editingTask" label="Status"> <!-- Status can only be changed when editing -->
					<el-select v-model="taskForm.columnId" placeholder="Select status">
						<el-option v-for="column in columns" :key="column.id" :label="column.name" :value="column.id" />
					</el-select>
				</el-form-item>
				<el-form-item label="Tags">
					<el-select v-model="taskForm.tags" multiple filterable allow-create default-first-option
						placeholder="Enter or select tags" style="width: 100%;">
						<!-- Pre-defined common tags can be added as el-option here if desired -->
					</el-select>
				</el-form-item>
				<el-form-item label="Assignees">
					<el-select v-model="taskForm.assignees" multiple filterable placeholder="Select assignees"
						style="width: 100%;">
						<el-option v-for="user in allUsers" :key="user.id" :label="user.name" :value="user.id" />
						<!-- Ensure allUsers provides appropriate key, label, and value -->
					</el-select>
				</el-form-item>
				<el-form-item label="Start Time">
					<el-date-picker v-model="taskForm.start_time" type="datetime"
						placeholder="Select start date and time" style="width: 100%;" />
				</el-form-item>
				<el-form-item label="End Time">
					<el-date-picker v-model="taskForm.end_time" type="datetime"
						placeholder="Select end date and time" style="width: 100%;" />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<!-- <el-button @click="showTaskDialog = false">Cancel</el-button>
					<el-button type="primary" @click="saveTask">Save</el-button> -->
					<el-button @click="showTaskDialog = false">Cancel</el-button>
					<el-button type="primary" @click="saveTask"
						:disabled="!taskForm.content.trim() || !taskForm.columnId">Save</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<style scoped>
.kanban-container {
	height: 100%;
	display: flex; /* Added to make container flex */
	flex-direction: column; /* Stack header and board vertically */
}

.kanban-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	flex-shrink: 0; /* Prevent header from shrinking */
}

.project-selector {
	display: flex;
	align-items: center;
	gap: 10px;
}

.kanban-board {
	display: flex;
	overflow-x: auto;
	flex-grow: 1; /* Allow board to take remaining space */
	min-height: 0; /* Added for proper flex-grow behavior in some browsers */
}

.columns-container {
	display: flex;
	gap: 20px;
	flex-grow: 1; /* Allow columns container to grow */
	padding-bottom: 10px; /* Add some padding at the bottom if columns overflow */
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300px;
}

.loading-container,
.error-container {
	padding: 40px;
	text-align: center; /* Center loading/error states */
}

/* Ensure dialog form items are well-spaced if content becomes too much */
.el-dialog .el-form-item {
	margin-bottom: 18px; /* Default is 22px, adjust as needed */
}
</style>