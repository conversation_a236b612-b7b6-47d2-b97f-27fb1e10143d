use crate::db::{Database, DatabaseConnection};
use crate::db::models::{Task, NewTask, UpdateTask, TaskStatus};
use anyhow::Result;
use chrono::Utc;
use sqlx::types::Json;
use uuid::Uuid;

/// Task repository
#[derive(Debug, Clone)]
pub struct TaskRepository {
    db: Database,
}

impl TaskRepository {
    /// Create a new task repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new task
    pub async fn create(&self, new_task: NewTask) -> Result<Task> {
        let id = Uuid::new_v4();
        let now = Utc::now();

        // project_id is now optional - can be None for projects (root tasks)
        let project_id = new_task.project_id;

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Prepare values to avoid temporary value issues
                let name = new_task.name.clone();
                let status_param = new_task.status;
                let description = new_task.description.clone();
                let tags_param = Json(new_task.tags.clone());
                let assignees_param = Json(new_task.assignees.clone());
                let start_time = new_task.start_time;
                let end_time = new_task.end_time;
                let parent_task_id = new_task.parent_task_id;
                let position = new_task.position;

                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, project_id, position,
                        created_at, updated_at
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(project_id)
                .bind(position)
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, project_id, position,
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(new_task.name)
                .bind(new_task.status)
                .bind(new_task.description)
                .bind(Json(new_task.tags))
                .bind(Json(new_task.assignees))
                .bind(new_task.start_time)
                .bind(new_task.end_time)
                .bind(new_task.parent_task_id)
                .bind(project_id)
                .bind(new_task.position)
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get a task by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get tasks by project ID
    pub async fn get_by_project_id(&self, project_id: Uuid) -> Result<Vec<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id = ?
                    ORDER BY position
                    "#
                )
                .bind(project_id)
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id = $1
                    ORDER BY position
                    "#
                )
                .bind(project_id)
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get all projects (tasks where project_id IS NULL)
    pub async fn get_projects(&self) -> Result<Vec<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id IS NULL
                    ORDER BY name
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id IS NULL
                    ORDER BY name
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get tasks by project ID and status
    pub async fn get_by_project_id_and_status(&self, project_id: Uuid, status: TaskStatus) -> Result<Vec<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Prepare the status value to avoid temporary value issues
                let status_str = match status {
                    TaskStatus::Todo => "todo",
                    TaskStatus::Doing => "doing",
                    TaskStatus::Review => "review",
                    TaskStatus::Done => "done",
                };

                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id = ? AND status = ?
                    ORDER BY position
                    "#
                )
                .bind(project_id)
                .bind(status_str)
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE project_id = $1 AND status = $2
                    ORDER BY position
                    "#
                )
                .bind(project_id)
                .bind(status)
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update a task
    pub async fn update(&self, id: Uuid, update_task: UpdateTask) -> Result<Task> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status_param = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags_param = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees_param = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = ?,
                        status = ?,
                        description = ?,
                        tags = ?,
                        assignees = ?,
                        start_time = ?,
                        end_time = ?,
                        parent_task_id = ?,
                        position = ?,
                        updated_at = ?
                    WHERE id = ?
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = $1,
                        status = $2,
                        description = $3,
                        tags = $4,
                        assignees = $5,
                        start_time = $6,
                        end_time = $7,
                        parent_task_id = $8,
                        position = $9,
                        updated_at = $10
                    WHERE id = $11
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        project_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status)
                .bind(description)
                .bind(tags)
                .bind(assignees)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete a task
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    r#"
                    DELETE FROM tasks
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // This code will only be compiled when the postgres feature is enabled
                sqlx::query(
                    r#"
                    DELETE FROM tasks
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update task positions
    pub async fn update_positions(&self, tasks: Vec<(Uuid, i32)>) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // For SQLite, use a transaction for better performance
                let mut tx = pool.begin().await?;

                for (id, position) in tasks {
                    sqlx::query(
                        r#"
                        UPDATE tasks
                        SET position = ?
                        WHERE id = ?
                        "#
                    )
                    .bind(position)
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
                }

                tx.commit().await?;
                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let mut tx = pool.begin().await?;

                for (id, position) in tasks {
                    sqlx::query(
                        r#"
                        UPDATE tasks
                        SET position = $1
                        WHERE id = $2
                        "#
                    )
                    .bind(position)
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
                }

                tx.commit().await?;
                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
