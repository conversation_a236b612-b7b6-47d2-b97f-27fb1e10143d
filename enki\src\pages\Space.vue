<script setup lang="ts">
import { useSpacesStore } from '@/stores/spacesStore';
import Breadcrumb from '@/components/Breadcrumb.vue';

defineProps({
    spaceId: {
        type: String,
        required: true
    }
});
const spacesStore = useSpacesStore();
</script>

<template>
    <el-container>
        <el-header>
            <Breadcrumb :space-id="spaceId" :space-name="spacesStore.spaces[spaceId].name" />
        </el-header>
        <el-main>
            <router-view />
        </el-main>
    </el-container>
</template>

<style scoped>
.el-main {
	min-width: 1080px;
	max-width: 1920px;
	width: 100%;
    margin: auto;
}
</style>
