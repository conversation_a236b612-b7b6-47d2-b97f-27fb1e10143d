<script setup lang="ts">
import { Edit, Delete } from '@element-plus/icons-vue';
import type { Task } from '@/types';
import type { PropType } from 'vue';
import { useDateFormat } from '@vueuse/core';

defineProps({
    content: {
        type: Object as PropType<Task>,
        required: true
    },
});

const emit = defineEmits(['edit-task', 'delete-task']);
</script>

<template>
    <el-card class="task-card" hover>
        <template #header>
            <div class="task-content">{{ content.name }}</div>
            <div class="task-actions">
                <el-button type="text" size="small" @click="emit('edit-task')">
                    <el-icon>
                        <Edit />
                    </el-icon>
                </el-button>
                <el-button type="text" size="small" @click="emit('delete-task')">
                    <el-icon>
                        <Delete />
                    </el-icon>
                </el-button>
            </div>
        </template>
        <template #default>
            <div>{{ content.description }}</div>
            <div class="flex">
                <el-tag v-for="tag in content.tags" :key="tag">{{ tag }}</el-tag>
            </div>
        </template>
        <template #footer>
            <div class="flex">
                <div v-for="assignee in content.assignees" :key="assignee">{{ assignee }}</div>
            </div>
            <div>{{ useDateFormat(content.end_time, 'DD/MM/YY HH:mm', { locales: 'fr-FR' }) }}</div>
        </template>
    </el-card>
</template>

<style scoped>
.task-card {
	margin-bottom: 8px;
}

:deep(.el-card__header),
:deep(.el-card__body),
:deep(.el-card__footer) {
    padding: 8px;
}

:deep(.el-card__header) {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	cursor: pointer;
}

.task-content {
	flex: 1;
	word-break: break-word;
    align-self: center;
}

.task-actions {
	display: flex;
}

.task-actions .el-button {
	padding: 0px;
}
</style>