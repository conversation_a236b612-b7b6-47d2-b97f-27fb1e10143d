use axum::{
    extract::{Path, State},
    http::Status<PERSON><PERSON>,
    Json,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::{
        db::{
        models::{
            NewTask,
            UpdateTask,
            TaskResponse,
            TaskStatus
        },
        repositories::TaskRepository
    },
    ServerState
};

/// Create a new task
pub async fn create_task(
    State(server_state): State<ServerState>,
    Path(project_id): Path<String>,
    <PERSON>son(mut new_task): Json<NewTask>,
) -> Result<(StatusCode, Json<TaskResponse>), (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let project_id = Uuid::parse_str(&project_id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    // Set parent task ID from path (project_id becomes parent_task_id)
    new_task.parent_task_id = Some(project_id);

    match repo.create(new_task).await {
        Ok(task) => Ok((StatusCode::CREATED, J<PERSON>(task.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a task by ID
pub async fn get_task(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<Json<TaskResponse>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    match repo.get_by_id(id).await {
        Ok(Some(task)) => Ok(Json(task.into())),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Task not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get tasks by project ID
pub async fn get_tasks(
    State(server_state): State<ServerState>,
    Path(project_id): Path<String>,
) -> Result<Json<Vec<TaskResponse>>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let project_id = Uuid::parse_str(&project_id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    match repo.get_by_project_id(project_id).await {
        Ok(tasks) => Ok(Json(tasks.into_iter().map(Into::into).collect())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a task
pub async fn update_task(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_task): Json<UpdateTask>,
) -> Result<Json<TaskResponse>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    match repo.update(id, update_task).await {
        Ok(task) => Ok(Json(task.into())),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, e.to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete a task
pub async fn delete_task(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    match repo.delete(id).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Move task request
#[derive(Debug, Serialize, Deserialize)]
pub struct MoveTaskRequest {
    pub status: String,
    pub position: i32,
}

/// Move a task
pub async fn move_task(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(move_request): Json<MoveTaskRequest>,
) -> Result<Json<TaskResponse>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    let status = TaskStatus::from(move_request.status.as_str());

    let update = UpdateTask {
        name: None,
        status: Some(status),
        description: None,
        tags: None,
        assignees: None,
        start_time: None,
        end_time: None,
        parent_task_id: None,
        position: Some(move_request.position),
    };

    match repo.update(id, update).await {
        Ok(task) => Ok(Json(task.into())),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, e.to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}
