use axum::{
    extract::{Path, State},
    http::Status<PERSON><PERSON>,
    Json,
};
use uuid::Uuid;
use crate::{
    db::{
        models::{NewTask, TaskStatus, TaskResponse},
        repositories::TaskRepository
    },
    ServerState
};

/// Create a new project (task with project_id = NULL)
pub async fn create_project(
    State(server_state): State<ServerState>,
    Json(new_project): Json<NewProjectRequest>,
) -> Result<(StatusCode, Json<TaskResponse>), (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let new_task = NewTask {
        name: new_project.name,
        status: TaskStatus::Todo,
        description: new_project.description,
        tags: vec![],
        assignees: vec![],
        start_time: None,
        end_time: None,
        parent_task_id: None,
        project_id: None, // This makes it a project
        position: 0,
    };

    match repo.create(new_task).await {
        Ok(task) => Ok((StatusCode::CREATED, Json(task.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a project by ID (task where project_id IS NULL)
pub async fn get_project(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<Json<TaskResponse>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    match repo.get_by_id(id).await {
        Ok(Some(task)) => {
            // Verify this is actually a project (project_id is None)
            if task.project_id.is_some() {
                return Err((StatusCode::NOT_FOUND, "Project not found".to_string()));
            }
            Ok(Json(task.into()))
        },
        Ok(None) => Err((StatusCode::NOT_FOUND, "Project not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get all projects (tasks where project_id IS NULL)
pub async fn get_projects(
    State(server_state): State<ServerState>,
) -> Result<Json<Vec<TaskResponse>>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    match repo.get_projects().await {
        Ok(projects) => Ok(Json(projects.into_iter().map(Into::into).collect())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a project
pub async fn update_project(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_project): Json<UpdateProjectRequest>,
) -> Result<Json<TaskResponse>, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    // First verify this is actually a project
    match repo.get_by_id(id).await {
        Ok(Some(task)) => {
            if task.project_id.is_some() {
                return Err((StatusCode::NOT_FOUND, "Project not found".to_string()));
            }
        },
        Ok(None) => return Err((StatusCode::NOT_FOUND, "Project not found".to_string())),
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }

    let update_task = crate::db::models::UpdateTask {
        name: update_project.name,
        status: None, // Don't change status for projects
        description: update_project.description,
        tags: None,
        assignees: None,
        start_time: None,
        end_time: None,
        parent_task_id: None,
        position: None,
    };

    match repo.update(id, update_task).await {
        Ok(task) => Ok(Json(task.into())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Delete a project
pub async fn delete_project(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = TaskRepository::new(server_state.db);

    let id = Uuid::parse_str(&id)
        .map_err(|e| (StatusCode::BAD_REQUEST, e.to_string()))?;

    // First verify this is actually a project
    match repo.get_by_id(id).await {
        Ok(Some(task)) => {
            if task.project_id.is_some() {
                return Err((StatusCode::NOT_FOUND, "Project not found".to_string()));
            }
        },
        Ok(None) => return Err((StatusCode::NOT_FOUND, "Project not found".to_string())),
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }

    match repo.delete(id).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Request structure for creating a new project
#[derive(serde::Deserialize)]
pub struct NewProjectRequest {
    pub name: String,
    pub description: Option<String>,
}

/// Request structure for updating a project
#[derive(serde::Deserialize)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
}
