<script setup lang="ts">
import { ArrowRight } from '@element-plus/icons-vue';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
    spaceId: String,
    spaceName: String
});

const route = useRoute();

function getRoutePath(path: string) {
    return '/' + path
        .slice(1)
        .split('/')
        .map((segment) => segment.startsWith(':') 
            ? route.params[segment.slice(1)]
            : segment
        )
        .join('/');
}

const breadcrumbs = computed(() => {
    const spaceCrumb = {
        label: props.spaceName,
        to: `/${props.spaceId}`,
    };
    const routeCrumbs = route.matched
        .slice(1)
        .map((routeItem) => ({
            label: routeItem.name,
            to: getRoutePath(routeItem.path),
        }));
    return [spaceCrumb, ...routeCrumbs];
});
</script>

<template>
    <el-breadcrumb :separator-icon="ArrowRight">
        <el-breadcrumb-item v-for="(crumb, index) in breadcrumbs" :to="crumb.to" :key="index">
            <h1>{{ crumb.label }}</h1>
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>