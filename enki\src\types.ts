export type Column = {
	id: TaskStatus;
	name: string;
}

export type TaskStatus = 'todo' | 'doing' | 'review' | 'done';

export type Task = {
	id: string;
	name: string;
	status: TaskStatus;
	description?: string;
	tags: string[];
	assignees: string[];
	start_time?: string;
	end_time?: string;
	parent_task_id?: string;
	position: number;
	created_at?: string;
	updated_at?: string;
}

export type NewTask = {
	name: string;
	status: TaskStatus;
	description?: string;
	tags?: string[];
	assignees?: string[];
	position: number;
	start_time?: string | null;
	end_time?: string | null;
}

export type UpdateTask = {
	name?: string;
	status?: TaskStatus;
	description?: string;
	tags?: string[];
	assignees?: string[];
	position?: number;
	start_time?: string | null;
	end_time?: string | null;
}

// Project is now just a Task with parent_task_id = undefined
export type Project = Task & { parent_task_id: undefined };

export type SpaceDefinition = {
    name: string;
    server: string;
}

export type Space = {
    projects: Project[];
}