use axum::{
    Router,
    routing::{get, post, put, delete},
    middleware,
};
use crate::{
    api::{
        auth::session_auth,
        handlers::{
            firebase_handlers::connect,
            project_task_handlers::{
                create_project, delete_project, get_project, get_projects, update_project
            },
            task_handlers::{
                create_task, delete_task, get_task, get_tasks, move_task, update_task
            },
        },
    },
    ServerState
};

/// Create API router
pub fn create_router(server_state: ServerState) -> Router {
    // Public routes (no authentication required)
    let public_routes = Router::new()
        .route("/connect", post(connect));

    // Protected routes (session authentication required)
    let protected_routes = Router::new()
        // Project routes
        .route("/projects", get(get_projects))
        .route("/projects", post(create_project))
        .route("/projects/{id}", get(get_project))
        .route("/projects/{id}", put(update_project))
        .route("/projects/{id}", delete(delete_project))

        // Task routes
        .route("/projects/{project_id}/tasks", get(get_tasks))
        .route("/projects/{project_id}/tasks", post(create_task))
        .route("/tasks/{id}", get(get_task))
        .route("/tasks/{id}", put(update_task))
        .route("/tasks/{id}", delete(delete_task))
        .route("/tasks/{id}/move", post(move_task))

        // Apply session authentication middleware to all protected routes
        .route_layer(middleware::from_fn_with_state(server_state.clone(), session_auth));

    // Combine public and protected routes
    Router::new()
        .merge(public_routes)
        .merge(protected_routes)
        .with_state(server_state)
}
